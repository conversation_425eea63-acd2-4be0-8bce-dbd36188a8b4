<template>
    <!-- 商品-商品分类 -->
    <div class="article-manager">
        <Card :bordered="false"
              dis-hover
              class="ivu-mt"
              :padding="0">
            <div class="new_card_pd">
                <!-- 筛选条件 -->
                <Form ref="artFrom"
                      :model="artFrom"
                      :label-width="labelWidth"
                      inline
                      :label-position="labelPosition"
                      @submit.native.prevent>
                    <FormItem label="关键字"
                              label-for="status2">
                        <Input placeholder="请输入uid/昵称/手机号"
                               v-model="artFrom.keywords"
                               clearable
                               class="input-add mr14" />
                    </FormItem>
                    <FormItem label="筛查项目"
                              label-for="status2">
                        <Input placeholder="请输入筛查项目"
                               clearable
                               v-model="artFrom.project"
                               class="input-add mr14" />
                    </FormItem>

                    <Button type="primary"
                            @click="userSearchs()">查询</Button>
                    <Button class="ResetSearch"
                            style="margin-left: 20px;"
                            @click="reset('artFrom')">重置</Button>
                </Form>
            </div>
        </Card>
        <Card :bordered="false"
              dis-hover
              class="ivu-mt">
            <!-- 商品分类表格 -->
            <vxe-table :data="tableData"
                       ref="xTable"
                       class="ivu-mt"
                       highlight-hover-row
                       :loading="loading"
                       header-row-class-name="false">
                <vxe-table-column field="uid"
                                  title="UID"
                                  tooltip></vxe-table-column>

                <vxe-table-column field="nickname"
                                  title="名称"
                                  tooltip></vxe-table-column>
                <vxe-table-column field="phone"
                                  title="题数"></vxe-table-column>
                <vxe-table-column field="project_name"
                                  title="参与人数"
                                  tooltip="true"></vxe-table-column>
                <vxe-table-column field="result"
                                  title="完成人数"
                                  tooltip="true"></vxe-table-column>
                <vxe-table-column field="score"
                                  title="生成报告数"
                                  tooltip="true"></vxe-table-column>
                <vxe-table-column field="date"
                                  title="操作">
                    <template v-slot="{ row, index }">
                        <a @click="edit(row)">编辑</a>
                        <a @click="showReportDetailDialog(row.id)"
                           style="margin-left:20px">详情</a>
                    </template>
                </vxe-table-column>
            </vxe-table>
            <div class="ivu-mt ivu-text-right">
                <Page :total="total"
                      :current="artFrom.page"
                      :page-size="artFrom.limit"
                      @on-change="changePatientPages"
                      show-elevator
                      show-total />
            </div>
        </Card>
        <Modal v-model="reportDetailModal"
               title="舌诊报告详情"
               :width="450"
               :footer-hide="true"
               :mask-closable="false">
            <div v-if="reportDetail">
                <div class="container">
                    <div class="report-card">
                        <div class="report-cards"
                             :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/gback.png)' }">
                            <div class="report-title">
                                <div class="report-text">{{physique_name }}</div>
                            </div>
                            <div class="report-subtitle">
                                <div class="subtitle-text">{{syndrome_name }}</div>
                            </div>
                            <div class="score-container">
                                <canvas class="gauge-canvas"
                                        id="gauge"></canvas>
                            </div>
                            <div style="float: right; margin-right: 20px">
                                <img :src="'https://67686161.com' + '/statics/images/product/xlog.png'"
                                     style="width: 100px; height: 100px" />
                            </div>
                        </div>
                        <div class="report-content">
                            <div class="section-title">
                                <img class="set_icon"
                                     style="width: 10px; height: 10px; margin-right: 10px;"
                                     :src="'https://67686161.com' + '/statics/images/product/titles.png'" />
                                <div class="section-text">体质辨识</div>
                            </div>
                            <div class="report-details">
                                <div class="detail-text">{{syndrome_introduction }}</div>
                            </div>
                        </div>
                        <div class="body-analysis">
                            <div class="analysis-title">
                                <div class="analysis-text">体质分析</div>
                            </div>
                            <div class="body-outline"
                                 :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/people.png)' }">
                                <div class="body-icon"></div>
                                <div class="body-labels">
                                    <!-- 左边标签 -->
                                    <div class="label"
                                         v-for="(answer, index) in leftAnswers"
                                         :key="`left-${index}`"
                                         :style="{ top: 3 + index * 15 + '%', left: '5%' }">
                                        {{ answer.name }}
                                    </div>
                                    <!-- 右边标签 -->
                                    <div class="label"
                                         v-for="(answer, index) in rightAnswers"
                                         :key="`right-${index}`"
                                         :style="{ top: 3 + index * 15 + '%', left: '75%' }">
                                        {{ answer.name }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 冠心病 -->
                        <div class="disease-section">
                            <div class="disease-details">
                                <div class="disease-text">{{physique_analysis }}</div>
                            </div>
                        </div>

                        <div class="report-card"
                             style="margin-top: 20px;">
                            <div class="body-analysisS"
                                 :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px'}">
                                <div class="analysis-titles">
                                    <div class="analysis-texts">典型特征</div>
                                </div>
                            </div>
                            <!-- 典型特征 -->
                            <div class="disease-section">
                                <div class="disease-details">
                                    <div class="disease-text">{{typical_symptom}}</div>
                                </div>
                            </div>
                        </div>

                        <div class="report-card"
                             style="margin-top: 20px;">
                            <div class="body-analysisS"
                                 :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px'}">
                                <div class="analysis-titles">
                                    <div class="analysis-texts">风险预警</div>
                                </div>
                            </div>
                            <!-- 风险预警 -->
                            <div class="Adisease-section">
                                <div class="disease-details">
                                    <div class="disease-text">{{risk_warning}}</div>
                                </div>
                            </div>
                        </div>
                        <!-- 舌面识别 -->
                        <div class="report-card"
                             style="margin-top: 20px;">
                            <div class="body-analysisS"
                                 :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px'}">
                                <div class="analysis-titles">
                                    <div class="analysis-texts">舌面识别</div>
                                </div>
                            </div>
                            <div class="disease-sectionAAA">
                                <div class="image-container">
                                    <img class="tongue-image"
                                         :src="'https://67686161.com'+'/statics/images/product/bj.png'"></img>
                                    <div class="background"
                                         :style="{ backgroundImage: 'url(' + surl + ')' }"></div>
                                </div>
                                <div v-for="(item,index) in shefeatures"
                                     style="margin-top: 20px;margin-bottom: 20px;">
                                    <div class="section-titleBBB">
                                        <img class="set_icon"
                                             style="width: 10px;height: 10px;margin-right: 10px;"
                                             :src="'https://67686161.com'+'/statics/images/product/titles.png'"></img>
                                        <div class="section-text">{{item.feature_group}}</div>
                                    </div>
                                    <div class="section-title">
                                        <div class="section-text"
                                             style="margin-left: 40px;">{{item.feature_name}}</div>
                                    </div>
                                    <div class="disease-details">
                                        <div class="disease-text">{{item.feature_interpret}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--面部识别-->
                        <div class="report-card"
                             style="margin-top: 20px;">
                            <div class="body-analysisS"
                                 :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px'}">
                                <div class="analysis-titles">
                                    <div class="analysis-texts">面部识别</div>
                                </div>
                            </div>
                            <div class="disease-sectionAAA">
                                <div class="image-container">
                                    <img class="tongue-image"
                                         :src="'https://67686161.com'+'/statics/images/product/bj.png'"></img>
                                    <div class="background"
                                         :style="{ backgroundImage: 'url(' + ff_image + ')' }"></div>
                                </div>
                                <div v-for="(item,index) in featuresface"
                                     style="margin-top: 20px;margin-bottom: 20px;">
                                    <div class="section-titleBBB">
                                        <img class="set_icon"
                                             style="width: 10px;height: 10px;margin-right: 10px;"
                                             :src="'https://67686161.com'+'/statics/images/product/titles.png'"></img>
                                        <div class="section-text">{{item.feature_group}}</div>
                                    </div>
                                    <div class="section-title">
                                        <div class="section-text"
                                             style="margin-left: 40px;">{{item.feature_name}}</div>
                                    </div>
                                    <div class="disease-details">
                                        <div class="disease-text">{{item.feature_interpret}}</div>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <!-- 运动建议 -->
                        <div class="report-card"
                             style="margin-top: 20px;"
                             v-for="(item,index) in sport"
                             :key="`sport-${index}`">
                            <div class="body-analysisS"
                                 :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px'}">
                                <div class="analysis-titles">
                                    <div class="analysis-texts">{{item.title}}</div>
                                </div>
                            </div>
                            <!-- 运动建议 -->
                            <div class="Adisease-section">
                                <div class="disease-details">
                                    <div class="disease-text">{{item.advice}}</div>
                                </div>
                            </div>
                        </div>

                        <!-- 饮食建议 -->
                        <div class="report-card"
                             style="margin-top: 20px;"
                             v-for="(item,index) in food"
                             :key="`food-${index}`">
                            <div class="body-analysisS"
                                 :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px'}">
                                <div class="analysis-titles">
                                    <div class="analysis-texts">{{item.title}}</div>
                                </div>
                            </div>
                            <!-- 饮食建议 -->
                            <div class="Adisease-section">
                                <div class="disease-details">
                                    <div class="disease-text">{{item.advice}}</div>
                                </div>
                            </div>
                        </div>

                        <!-- 外治建议 -->
                        <div class="report-card"
                             style="margin-top: 20px;"
                             v-for="(item, index) in treatment"
                             :key="`treatment-${index}`">
                            <div class="body-analysisS"
                                 :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px'}">
                                <div class="analysis-titles">
                                    <div class="analysis-texts">{{item.title}}</div>
                                </div>
                            </div>
                            <!-- 外治建议 -->
                            <div class="Adisease-section">
                                <div class="disease-details">
                                    <div class="disease-text">{{item.advice}}</div>
                                </div>
                            </div>
                        </div>

                        <!--音乐建议-->
                        <div class="report-card"
                             style="margin-top: 20px;"
                             v-for="(item,index) in music"
                             :key="`music-${index}`">
                            <div class="body-analysisS"
                                 :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px'}">
                                <div class="analysis-titles">
                                    <div class="analysis-texts">{{item.title}}</div>
                                </div>
                            </div>
                            <!--音乐建议-->
                            <div class="Adisease-section">
                                <div class="disease-details">
                                    <div class="disease-text">{{item.advice}}</div>
                                </div>
                            </div>
                        </div>

                        <!--起居建议-->
                        <div class="report-card"
                             style="margin-top: 20px;"
                             v-for="(item,index) in sleep"
                             :key="`sleep-${index}`">
                            <div class="body-analysisS"
                                 :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px'}">
                                <div class="analysis-titles">
                                    <div class="analysis-texts">{{item.title}}</div>
                                </div>
                            </div>
                            <!--起居建议-->
                            <div class="Adisease-section">
                                <div class="disease-details">
                                    <div class="disease-text">{{item.advice}}</div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </Modal>
        <!-- 编辑量表弹窗 -->
        <Modal v-model="showEditModal"
               title="编辑量表"
               width="60%"
               :mask-closable="false">
            <Tabs v-model="activeTab">
                <!-- 基础信息 -->
                <TabPane label="基础信息"
                         name="basic">
                    <Form :model="editForm"
                          label-position="left"
                          :label-width="110">
                        <!-- 量表名称 -->
                        <FormItem label="量表名称">
                            <Input v-model="editForm.name"
                                   placeholder="请输入量表名称" />
                        </FormItem>

                        <!-- 作答时间 -->
                        <FormItem label="作答时间(分钟)">
                            <InputNumber v-model="editForm.duration"
                                         :min="1"
                                         :max="999"
                                         style="width: 100%" />
                        </FormItem>

                        <!-- 是否可重复填写 -->
                        <FormItem label="是否可重复填写">
                            <Select v-model="editForm.repeatable"
                                    style="width: 100%">
                                <Option value="1">是</Option>
                                <Option value="0">否</Option>
                            </Select>
                        </FormItem>

                        <!-- 已测人数 -->
                        <FormItem label="已测人数">
                            <InputNumber v-model="editForm.testedCount"
                                         :min="0"
                                         style="width: 100%" />
                        </FormItem>

                        <!-- 量表轮播图（最多 6 张） -->
                        <FormItem label="量表轮播图">
                            <p style="font-size:12px;color:#999;margin-bottom:6px;">最多可上传 6 张，建议尺寸 750×420</p>
                            <Upload multiple
                                    :before-upload="handleBannerUpload"
                                    :format="['jpg','jpeg','png']"
                                    :max-size="2048"
                                    action="">
                                <Button icon="ios-cloud-upload-outline">上传图片</Button>
                            </Upload>

                            <div style="display:flex;flex-wrap:wrap;gap:8px;margin-top:10px">
                                <div v-for="(img,idx) in editForm.carouselImages"
                                     :key="idx"
                                     style="position:relative;">
                                    <img :src="img.img"
                                         width="100"
                                         height="100"
                                         style="object-fit:cover;border-radius:4px" />
                                    <Icon type="ios-trash-outline"
                                          size="20"
                                          @click="removeImage('carouselImages', idx)"
                                          style="position:absolute;top:2px;right:2px;color:#fff;background:#ed4014;border-radius:50%;padding:2px;cursor:pointer" />
                                </div>
                            </div>
                        </FormItem>

                        <!-- 量表分享图（1 张） -->
                        <FormItem label="量表分享图">
                            <p style="font-size:12px;color:#999;margin-bottom:6px;">建议尺寸 750×420</p>
                            <Upload :before-upload="handleShareUpload"
                                    :format="['jpg','jpeg','png']"
                                    :max-size="2048"
                                    action="">
                                <Button icon="ios-cloud-upload-outline">上传图片</Button>
                            </Upload>

                            <div style="display:flex;flex-wrap:wrap;gap:8px;margin-top:10px">
                                <div v-for="(img,idx) in editForm.shareImage"
                                     :key="idx"
                                     style="position:relative;">
                                    <img :src="img.img"
                                         width="100"
                                         height="100"
                                         style="object-fit:cover;border-radius:4px" />
                                    <Icon type="ios-trash-outline"
                                          size="20"
                                          @click="removeImage('shareImage', idx)"
                                          style="position:absolute;top:2px;right:2px;color:#fff;background:#ed4014;border-radius:50%;padding:2px;cursor:pointer" />
                                </div>
                            </div>
                        </FormItem>

                        <!-- 客服二维码（1 张） -->
                        <FormItem label="客服二维码">
                            <p style="font-size:12px;color:#999;margin-bottom:6px;">建议尺寸 750×420</p>
                            <Upload :before-upload="handleQrcodeUpload"
                                    :format="['jpg','jpeg','png']"
                                    :max-size="2048"
                                    action="">
                                <Button icon="ios-cloud-upload-outline">上传图片</Button>
                            </Upload>

                            <div style="display:flex;flex-wrap:wrap;gap:8px;margin-top:10px">
                                <div v-for="(img,idx) in editForm.qrCode"
                                     :key="idx"
                                     style="position:relative;">
                                    <img :src="img.img"
                                         width="100"
                                         height="100"
                                         style="object-fit:cover;border-radius:4px" />
                                    <Icon type="ios-trash-outline"
                                          size="20"
                                          @click="removeImage('qrCode', idx)"
                                          style="position:absolute;top:2px;right:2px;color:#fff;background:#ed4014;border-radius:50%;padding:2px;cursor:pointer" />
                                </div>
                            </div>
                        </FormItem>

                        <!-- 量表头图（1 张） -->
                        <FormItem label="量表头图">
                            <p style="font-size:12px;color:#999;margin-bottom:6px;">用于头图和报告页，建议尺寸 750×420</p>
                            <Upload :before-upload="handleHeadUpload"
                                    :format="['jpg','jpeg','png']"
                                    :max-size="2048"
                                    action="">
                                <Button icon="ios-cloud-upload-outline">上传图片</Button>
                            </Upload>

                            <div style="display:flex;flex-wrap:wrap;gap:8px;margin-top:10px">
                                <div v-for="(img,idx) in editForm.headImage"
                                     :key="idx"
                                     style="position:relative;">
                                    <img :src="img.img"
                                         width="100"
                                         height="100"
                                         style="object-fit:cover;border-radius:4px" />
                                    <Icon type="ios-trash-outline"
                                          size="20"
                                          @click="removeImage('headImage', idx)"
                                          style="position:absolute;top:2px;right:2px;color:#fff;background:#ed4014;border-radius:50%;padding:2px;cursor:pointer" />
                                </div>
                            </div>
                        </FormItem>
                    </Form>
                </TabPane>

                <!-- 量表详情 -->
                <TabPane label="量表详情"
                         name="detail">

                    <wangeditor style="width: 100%"
                                :content="editForm.detailContent">
                    </wangeditor>

                </TabPane>

                <!-- 协议详情 -->
                <TabPane label="协议详情"
                         name="agreement">

                    <wangeditor style="width: 100%"
                                :content="editForm.agreementContent">
                    </wangeditor>

                </TabPane>
            </Tabs>

            <!-- 底部按钮 -->
            <div slot="footer"
                 style="text-align:right">
                <Button @click="showEditModal=false">取消</Button>
                <Button type="primary"
                        @click="handleSaveEdit">保存</Button>
            </div>
        </Modal>
    </div>
</template>

<script>
    import { mapState } from 'vuex';
    import wangeditor from '@/components/wangEditor/index.vue';
    import { getdiagnosisList, getreportdetail, getdiagnosisdetail } from "@/api/user";

    export default {
        name: 'product_productClassify',
        data () {
            return {
                showEditModal: false,          // 弹窗显隐
                activeTab: 'basic',            // 默认标签
                editForm: {
                    name: '',
                    duration: 30,
                    repeatable: '1',
                    testedCount: 0,
                    carouselImages: [],   // [{img:'xxx'},...]
                    shareImage: [],       // 单张
                    qrCode: [],           // 单张
                    headImage: []         // 单张
                },
                grid: {
                    xl: 7,
                    lg: 7,
                    md: 12,
                    sm: 24,
                    xs: 24
                },
                loading: false,
                artFrom: {
                    page: 1,
                    keywords: '',
                    limit: 10,
                    project: ''   // 新增：康训项目
                },
                total: 0,
                tableData: [],
                showModal: false,
                reportDetailModal: false, // 舌诊报告详情弹窗状态
                reportDetail: null, // 舌诊报告详情数据
                score: '', // 示例分数
                resData: [],
                physique_name: '',
                physique_analysis: '',
                syndrome_introduction: '',
                syndrome_name: '',
                answers: [],
                food: [],
                music: [],
                sport: [],
                sleep: [],
                treatment: [],
                surl: '',
                ff_image: '',
                risk_warning: '',
                featuresface: [],
                shefeatures: [],
                typical_symptom: '',
                typical_symptom_arr: [],
                colors: ['#4a90e2', '#ff7e5f', '#50e3c2', '#f6b93b', '#9e9e9e'],
                fontSizes: [12, 14, 16, 18, 20],
                detailId: ''

            }
        },
        components: {
            wangeditor,
        },
        computed: {
            ...mapState('admin/layout', [
                'isMobile'
            ]),
            ...mapState('admin/userLevel', [
                'categoryId'
            ]),
            labelWidth () {
                return this.isMobile ? undefined : 96;
            },
            labelPosition () {
                return this.isMobile ? 'top' : 'right';
            },
            leftAnswers () {
                return this.answers.filter((answer, index) => index % 2 === 0);
            },
            rightAnswers () {
                return this.answers.filter((answer, index) => index % 2 === 1);
            },


        },
        watch: {

            reportDetailModal (value) {
                if (!value) {
                    // 弹窗关闭时清除 canvas
                    const canvas = document.getElementById('gauge');
                    if (canvas) {
                        const ctx = canvas.getContext('2d');
                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                    }
                }
            }
        },
        mounted () {
            this.getList();
        },
        methods: {
            edit (row) {
                // 这里用深拷贝，避免污染原行数据
                this.editForm = {
                    id: row.id,
                    name: row.name || '示例量表',
                    duration: row.duration || 30,
                    repeatable: row.repeatable || '1',
                    testedCount: row.testedCount || 0,
                    detailContent: row.detailContent || '<p>量表详情内容</p>',
                    agreementContent: row.agreementContent || '<p>协议详情内容</p>'
                };
                this.activeTab = 'basic';
                this.showEditModal = true;
            },

            /**
             * 保存编辑
             */
            handleSaveEdit () {
                // TODO: 调用接口保存 this.editForm
                console.log('保存量表：', this.editForm);
                this.$Message.success('保存成功');
                this.showEditModal = false;
                this.getList(); // 重新拉列表（可选）
            },
            showReportDetailDialog (report) {
                console.log(report, '9999')
                this.reportDetail = report;
                this.detail(report)
                this.reportDetailModal = true;
            },

            //获取详情数据
            detail (report) {
                console.log(report, '大哥哥')
                getdiagnosisdetail({ id: report }).then(res => {
                    // 打印传递的 resData
                    this.resData = res.data; // 将数据赋值给页面的 data
                    console.log(res.data, res.data.score, '罗鑫')
                    //分数
                    this.score = res.data.score
                    //体质名称
                    this.physique_name = this.resData.physique_name
                    //体质辨识
                    this.syndrome_introduction = this.resData.syndrome_introduction
                    this.syndrome_name = this.resData.syndrome_name
                    //获取选中答题是
                    this.answers = this.resData.answers

                    //饮食建议
                    this.food = this.resData.advices.food
                    //音乐建议
                    this.music = this.resData.advices.music
                    //运动建议
                    this.sport = this.resData.advices.sport
                    //起居建议
                    this.sleep = this.resData.advices.sleep
                    //外治建议
                    this.treatment = this.resData.advices.treatment
                    //舌头
                    this.surl = this.resData.tf_detect_matches.url
                    this.physique_analysis = this.resData.physique_analysis
                    this.risk_warning = this.resData.risk_warning
                    //面部
                    this.featuresface = this.resData.features_arr[0]
                    //舌部
                    this.shefeatures = this.resData.features_arr[1]
                    this.typical_symptom = this.resData.typical_symptom
                    this.typical_symptom_arr = this.resData.typical_symptom_arr || []; // 确保赋值
                    this.ff_image = this.resData.ff_image
                    // 确保 canvas 元素已渲染后再绘制
                    this.$nextTick(() => {
                        this.drawGauge();
                    });
                })
            },



            drawGauge () {
                // 获取 canvas 元素
                const canvas = document.getElementById('gauge');
                if (!canvas) {
                    console.error('Canvas element not found');
                    return;
                }

                // 获取 canvas 上下文
                const ctx = canvas.getContext('2d');
                if (!ctx) {
                    console.error('Canvas context not found');
                    return;
                }
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const radius = Math.min(centerX, centerY) - 10;
                const startAngle = Math.PI + Math.PI * 0.005; // 调整起始角度，闭合90%
                const endAngle = Math.PI * 2 - Math.PI * 0.005; // 调整结束角度
                const maxScore = 100;
                const minScore = 50;
                const percentage = (this.score - minScore) / (maxScore - minScore);
                const sweepAngle = (endAngle - startAngle) * percentage;

                // 绘制背景圆弧
                const ticks = [50, 60, 70, 80, 90, 100];
                const colors = ['#FE8B86', '#FEB936', '#9EE202', '#24ED05', '#21E303']; // 颜色数组
                for (let i = 0; i < ticks.length - 1; i++) {
                    const tickPercentage = (ticks[i] - minScore) / (maxScore - minScore);
                    const nextTickPercentage = (ticks[i + 1] - minScore) / (maxScore - minScore);
                    const tickAngle = startAngle + tickPercentage * (endAngle - startAngle);
                    const nextTickAngle = startAngle + nextTickPercentage * (endAngle - startAngle);

                    ctx.beginPath();
                    ctx.arc(centerX, centerY, radius, tickAngle, nextTickAngle);
                    ctx.strokeStyle = colors[i];
                    ctx.lineWidth = 10;
                    ctx.stroke();
                }

                // 绘制刻度值（放在圆圈里面）
                for (let i = 0; i < ticks.length; i++) {
                    const tickPercentage = (ticks[i] - minScore) / (maxScore - minScore);
                    const tickAngle = startAngle + tickPercentage * (endAngle - startAngle);

                    // 绘制刻度值
                    ctx.font = '12px Arial';
                    ctx.fillStyle = '#fff'; // 设置数字颜色为白色
                    ctx.textAlign = 'center';
                    ctx.fillText(
                        ticks[i].toString(),
                        centerX + radius * 0.7 * Math.cos(tickAngle), // 调整位置，放在圆圈里面
                        centerY + radius * 0.7 * Math.sin(tickAngle) + 5
                    );
                }

                // 绘制指针（缩短指针长度）
                ctx.beginPath();
                ctx.moveTo(centerX, centerY); // 指针起始点
                ctx.lineTo(
                    centerX + radius * 0.6 * Math.cos(startAngle + sweepAngle), // 指针终点（缩短长度）
                    centerY + radius * 0.6 * Math.sin(startAngle + sweepAngle)
                );
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 3;
                ctx.stroke();

                // 绘制中心点
                ctx.beginPath();
                ctx.arc(centerX, centerY, 5, 0, Math.PI * 2);
                ctx.fillStyle = '#fff';
                ctx.fill();

                // 绘制分数
                ctx.font = '24px Arial';
                ctx.fillStyle = '#fff';
                ctx.textAlign = 'center';
                ctx.fillText(this.score.toString(), centerX, centerY + 30); // 调整分数位置
            },
            updateScore (newScore) {
                this.score = newScore;
                this.drawGauge();
            },

            changePatientPages (page) {
                this.artFrom.page = page;
                this.getList();
            },
            // 列表
            getList () {
                this.loading = true;
                getdiagnosisList(this.artFrom).then(async res => {
                    console.log(res, '罗鑫')
                    this.tableData = res.data.list;
                    this.total = res.data.count;
                    this.loading = false;
                }).catch(res => {
                    this.loading = false;
                    this.$Message.error(res.msg);
                })
            },
            pageChange (index) {
                this.artFrom.page = index;
                this.getList();
            },
            reset () {
                this.artFrom.project = ''
                this.artFrom.keywords = '';  // 新增
                this.getList(this.artFrom);
            },
            // 表格搜索
            userSearchs () {
                this.artFrom.page = 1;
                this.getList();
            },

        }
    }
</script>
<style scoped lang="stylus">
    .treeSel >>>.ivu-select-dropdown-list
        padding 0 10px !important
        box-sizing border-box
    .tabBox_img
        width 36px
        height 36px
        border-radius 4px
        cursor pointer
        img
            width 100%
            height 100%
    /deep/.ivu-input
        font-size 14px !important
    .project-time-row
        display flex
        margin-bottom 10px
    .add-project-button
        margin-left 90px
        margin-bottom 30px
        border solid 1px #2D8CF0
        color #2D8CF0
        line-hight 10px
        .suffix
            position absolute
            right 10px
            top 50%
            transform translateY(-50%)
            font-size 14px
            color #1A1A1A
            pointer-events none
</style>
<style lang="less" scoped>
    .container {
        padding: 20px;
        background-color: #f5f5f5;
        height: 100%;
    }

    .gauge-canvas {
        width: 230px;
        height: 135px;
    }

    .report-card {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: relative; /* 添加 position 属性 */
    }

    .report-cards {
        height: 480px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        position: relative; /* 添加 position 属性 */
        z-index: 1; /* 设置 z-index 为 1，使其位于下层 */
    }

    .report-title {
        color: white;
        padding: 15px;
        text-align: left;
        font-size: 30px;
        font-weight: Semibold;
    }

    .report-subtitle {
        margin-top: 30px;
        color: white;
        padding: 10px;
        font-size: 14px;
        width: 150px;
        height: 40px;
        margin-left: 20px;
        text-align: left;
        border-radius: 10px;
        background-color: #1272cb; /* 基础背景色 */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5); /* 添加阴影效果 */
    }

    .score-container {
        position: absolute;
        top: 60px;
        right: -40px;
    }

    .score-gauge {
        width: 120px;
        height: 120px;
    }

    .score-value {
        font-size: 24px;
        font-weight: bold;
        margin-left: 10px;
    }

    .report-content {
        padding: 20px;
        background-color: #eff8ff;
        border-radius: 10px;
        position: absolute;
        top: 20px;
        width: calc(128% - 40px);
        z-index: 2; /* 设置 z-index 为 2，使其位于上层 */
        margin-top: 220px;
        margin-left: 20px;
    }

    .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .section-titleBBB {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding-left: 20px;
    }

    .section-text {
        font-size: 16px;
        font-weight: bold;
        color: #4a90e2;
    }

    .report-details {
        line-height: 1.6;
        color: #666;
    }

    .body-analysis {
        padding: 20px;
        // border-top: 1px solid #eee;
    }

    .body-analysisS {
        padding: 10px 0px 0px 40px;
        background-size: cover;
        background-position: center;
        position: relative; /* 添加 position 属性 */
        background-color: #f0f8ff;
        // border-radius: 10px;
        height: 40px; /* 设置背景图的高度 */
    }

    .analysis-title {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .analysis-titles {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        position: relative;
        z-index: 2; /* 确保文字显示在背景图的上层 */
    }

    .analysis-text {
        font-size: 16px;
        font-weight: bold;
        color: #4a90e2;
    }

    .analysis-texts {
        font-size: 16px;
        font-weight: bold;
        color: white;
    }

    .body-outline {
        position: relative;
        height: 400px;
        background-color: #f0f8ff;
        border-radius: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .body-icon {
        width: 200px;
        height: 400px;
        border-radius: 100px;
        position: relative;
    }

    .body-labels {
        position: absolute;
        width: 100%;
        height: 100%;
    }

    .label {
        position: absolute;
        background-color: rgba(255, 255, 255, 0.8);
        padding: 5px 10px;
        border-radius: 5px;
        font-size: 12px;
        color: #333;
    }

    /* 疾病部分样式 */
    .disease-section {
        padding: 10px;
        background-color: white;
        border-radius: 10px;
        margin-left: 20px;
        margin-right: 20px;
        // box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    /* 疾病部分样式 */
    .disease-sectionAAA {
        padding: 10px;
        background-color: white;
        border-radius: 10px;
        margin-right: 20px;
    }

    .disease-details {
        line-height: 1.6;
        color: #444;
        padding: 10px;
        background-color: white;
        border-radius: 8px;
        // box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .disease-text {
        margin-bottom: 8px;
        font-size: 14px;
    }

    .Adisease-section {
        background-color: white;
        border-radius: 10px;
        margin-left: 20px;
        margin-right: 20px;
    }

    .image-container {
        width: 300px;
        height: 300px;
        border-radius: 10px;
        overflow: hidden;
        margin: 20px 0;
        position: relative;
    }

    .tongue-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
    }
    .background {
        width: 62%;
        height: 70%;
        background-position: center; /* 背景图片居中 */
        background-repeat: no-repeat; /* 不重复背景图片 */
        background-size: cover; /* 背景图片覆盖整个容器 */
        position: absolute;
        margin-top: -88%;
        margin-left: 19%;
        border-radius: 15px;
        z-index: 2;
    }
</style>