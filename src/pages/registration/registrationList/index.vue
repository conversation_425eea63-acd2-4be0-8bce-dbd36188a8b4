<template>
    <!-- 商品-商品分类 -->
    <div class="article-manager">
        <Card :bordered="false"
              dis-hover
              class="ivu-mt"
              :padding="0">
            <div class="new_card_pd">
                <!-- 筛选条件 -->
                <Form ref="artFrom"
                      :model="artFrom"
                      :label-width="labelWidth"
                      inline
                      :label-position="labelPosition"
                      @submit.native.prevent>
                    <FormItem label="科室名称"
                              label-for="status2">
                        <Input placeholder="请输入科室名称"
                               clearable
                               v-model="artFrom.dept_name"
                               class="input-add mr14" />
                    </FormItem>

                    <Button type="primary"
                            @click="userSearchs()">查询</Button>
                    <Button class="ResetSearch"
                            style="margin-left: 20px;"
                            @click="reset('artFrom')">重置</Button>
                </Form>
            </div>
        </Card>
        <Card :bordered="false"
              dis-hover
              class="ivu-mt">
            <div slot="title"
                 style="display:flex;align-items:center;">
                <!-- <span>科室列表</span> -->
                <Button type="primary"
                        @click="openAddModal">添加</Button>
                <Button type="primary"
                        @click="openBannerModal"
                        style="margin-left:12px">添加科室轮播图</Button>
            </div>
            <!-- 商品分类表格 -->
            <vxe-table :data="tableData"
                       ref="xTable"
                       class="ivu-mt"
                       highlight-hover-row
                       :loading="loading"
                       header-row-class-name="false">
                <vxe-table-column field="dept_name"
                                  title="科室名称"></vxe-table-column>
                <vxe-column field="image"
                            title="科室图标">
                    <template v-slot="{ row }">
                        <viewer>
                            <div class="tabBox_img">
                                <img v-lazy="row.image" />
                            </div>
                        </viewer>
                    </template>
                </vxe-column>
                <vxe-table-column field="status"
                                  title="状态"
                                  min-width="120">
                    <template v-slot="{ row }">
                        <i-switch v-model="row.status"
                                  :value="row.status"
                                  :true-value="1"
                                  :false-value="0"
                                  @on-change="onchangeIsShow(row)"
                                  size="large">
                            <span slot="open">显示</span>
                            <span slot="close">隐藏</span>
                        </i-switch>
                    </template>
                </vxe-table-column>
                <vxe-table-column field="desc"
                                  title="诊疗范围"
                                  tooltip="true"></vxe-table-column>
                <vxe-table-column field="date"
                                  title="操作">
                    <template v-slot="{ row, index }">
                        <Divider type="vertical" />
                        <a @click="showReportDetailDialog(row.id)">编辑</a>
                        <a @click="showDetailDialog(row.id)"
                           style="margin-left:20px">详情</a>
                        <a @click="del(row.id)"
                           style="margin-left:20px;color:red">删除</a>

                    </template>
                </vxe-table-column>
            </vxe-table>
            <div class="ivu-mt ivu-text-right">
                <Page :total="total"
                      :current="artFrom.page"
                      :page-size="artFrom.limit"
                      @on-change="changePatientPages"
                      show-elevator
                      show-total />
            </div>
        </Card>

        <!-- 编辑弹窗 -->
        <Modal v-model="editModal"
               title="编辑科室"
               :mask-closable="false"
               width="600">
            <Form ref="editForm"
                  :model="editForm"
                  :label-width="80">
                <FormItem label="科室名称">
                    <Input v-model="editForm.dept_name"
                           placeholder="请输入科室名称" />
                </FormItem>

                <FormItem label="诊疗范围">
                    <Input v-model="editForm.desc"
                           type="textarea"
                           :rows="3"
                           placeholder="请输入诊疗范围" />
                </FormItem>

                <!-- 科室图标 单图上传 -->
                <!-- 科室图标（单图） -->
                <FormItem label="科室图标">
                    <Upload ref="iconUpload"
                            :before-upload="beforeIconUpload"
                            :show-upload-list="false"
                            :format="['jpg','jpeg','png']"
                            :max-size="2048"
                            :on-format-error="handleFormatError"
                            :on-exceeded-size="handleMaxSize"
                            action="">
                        <Button icon="ios-cloud-upload-outline">上传图标</Button>
                    </Upload>

                    <!-- 回显 + 删除 -->
                    <div v-if="editForm.image"
                         class="demo-upload-list"
                         style="margin-right: 10px;">
                        <img :src="editForm.image"
                             style="width:100px;height:100px;margin-top:10px;object-fit:cover" />
                        <div class="demo-upload-list-cover"
                             style="margin-left: 40px;">
                            <Icon type="ios-trash-outline"
                                  size="24"
                                  @click="editForm.image=''"></Icon>
                        </div>
                    </div>
                </FormItem>

                <!-- 科室详情轮播图（多图） -->
                <FormItem label="轮播图">
                    <Upload multiple
                            :before-upload="beforeDetailUpload"
                            :on-remove="removeDetailImage"
                            :format="['jpg','jpeg','png']"
                            :max-size="2048"
                            action="">
                        <Button icon="ios-cloud-upload-outline">上传轮播图</Button>
                    </Upload>

                    <!-- 已上传图片列表 -->
                    <div style="display: flex;
flex-wrap: nowrap;">
                        <div class="demo-upload-list"
                             style="margin-right:10px"
                             v-for="(img, idx) in editForm.banners"
                             :key="idx">
                            <img :src="img"
                                 style="width:100px;height:100px;margin-top:10px;object-fit:cover" />
                            <div class="demo-upload-list-cover"
                                 style="margin-left: 40px;">
                                <Icon type="ios-trash-outline"
                                      size="24"
                                      @click="editForm.banners.splice(idx,1)"></Icon>
                            </div>
                        </div>
                    </div>
                </FormItem>
            </Form>

            <div slot="footer">
                <Button @click="editModal=false">取消</Button>
                <Button type="primary"
                        :loading="btnLoading"
                        @click="submitEdit">确定</Button>
            </div>
        </Modal>

        <!-- ===== 详情弹窗（只读） ===== -->
        <Modal v-model="detailModal"
               title="科室详情"
               :mask-closable="false"
               width="600">
            <Form :label-width="80">
                <FormItem label="科室名称">
                    <span>{{ detailForm.dept_name }}</span>
                </FormItem>

                <FormItem label="诊疗范围">
                    <span>{{ detailForm.desc }}</span>
                </FormItem>

                <FormItem label="科室图标">
                    <img v-if="detailForm.image"
                         :src="detailForm.image"
                         style="width:100px;height:100px;object-fit:cover;border-radius:4px">
                </FormItem>

                <FormItem label="轮播图">
                    <div style="display:flex;flex-wrap:nowrap;gap:10px;overflow-x:auto;">
                        <img v-for="(img,idx) in detailForm.banners"
                             :key="idx"
                             :src="img"
                             style="width:100px;height:100px;object-fit:cover;border-radius:4px;flex-shrink:0">
                    </div>
                </FormItem>
            </Form>

            <div slot="footer">
                <Button @click="detailModal=false">关闭</Button>
            </div>
        </Modal>
        <!-- ===== 新增弹窗 ===== -->
        <Modal v-model="addModal"
               title="新增科室"
               :mask-closable="false"
               width="600">
            <Form ref="addForm"
                  :model="addForm"
                  :label-width="80">
                <FormItem label="科室名称">
                    <Input v-model="addForm.dept_name"
                           placeholder="请输入科室名称" />
                </FormItem>

                <FormItem label="诊疗范围">
                    <Input v-model="addForm.desc"
                           type="textarea"
                           :rows="3"
                           placeholder="请输入诊疗范围" />
                </FormItem>

                <!-- 科室图标 单图上传 -->
                <FormItem label="科室图标">
                    <Upload ref="addIconUpload"
                            :before-upload="beforeAddIconUpload"
                            :show-upload-list="false"
                            :format="['jpg','jpeg','png']"
                            :max-size="2048"
                            :on-format-error="handleFormatError"
                            :on-exceeded-size="handleMaxSize"
                            action="">
                        <Button icon="ios-cloud-upload-outline">上传图标</Button>
                    </Upload>
                    <div v-if="addForm.image"
                         class="demo-upload-list">
                        <img :src="addForm.image"
                             style="width:100px;height:100px;object-fit:cover;margin-top:10px" />
                        <div class="demo-upload-list-cover"
                             style="margin-left: 40px;">
                            <Icon type="ios-trash-outline"
                                  size="24"
                                  @click="addForm.image=''"></Icon>
                        </div>
                    </div>
                </FormItem>

                <!-- 轮播图 多图上传 -->
                <FormItem label="轮播图">
                    <Upload multiple
                            :before-upload="beforeAddDetailUpload"
                            :format="['jpg','jpeg','png']"
                            :max-size="2048"
                            action="">
                        <Button icon="ios-cloud-upload-outline">上传轮播图</Button>
                    </Upload>
                    <div style="display:flex;flex-wrap:nowrap;gap:10px;overflow-x:auto;">
                        <div class="demo-upload-list"
                             v-for="(img, idx) in addForm.banners"
                             :key="idx">
                            <img :src="img"
                                 style="width:100px;height:100px;margin-top:10px;object-fit:cover;" />
                            <div class="demo-upload-list-cover"
                                 style="margin-left: 40px;">
                                <Icon type="ios-trash-outline"
                                      size="24"
                                      @click="addForm.banners.splice(idx,1)"></Icon>
                            </div>
                        </div>
                    </div>
                </FormItem>
            </Form>

            <div slot="footer">
                <Button @click="addModal=false">取消</Button>
                <Button type="primary"
                        :loading="btnLoading"
                        @click="submitAdd">确定</Button>
            </div>
        </Modal>

        <Modal v-model="bannerModal"
               title="科室轮播图列表"
               width="600">
            <Upload multiple
                    :before-upload="handleBannerUpload"
                    :format="['jpg','jpeg','png']"
                    :max-size="2048"
                    action="">
                <Button icon="ios-cloud-upload-outline">上传图片</Button>
            </Upload>

            <!-- 已上传图片 -->
            <div style="display:flex;flex-wrap:wrap;gap:8px;margin-top:10px">
                <div v-for="(img,idx) in bannerImages"
                     :key="idx"
                     style="position:relative;">
                    <img :src="img.img"
                         width="100"
                         height="100"
                         style="object-fit:cover;border-radius:4px" />
                    <Icon type="ios-trash-outline"
                          size="20"
                          @click="removeBannerImage(idx)"
                          style="position:absolute;top:2px;right:2px;color:#fff;background:#ed4014;border-radius:50%;padding:2px;cursor:pointer" />
                </div>
            </div>

            <div slot="footer">
                <Button @click="bannerModal=false">取消</Button>
                <Button type="primary"
                        @click="submitBanner">保存</Button>
            </div>
        </Modal>
    </div>
</template>

<script>
    import util from '@/libs/util';
    import axios from "axios";
    import { mapState } from 'vuex';
    import { departmentList, departmentUpdate, departmentAdd, deldepartment, banneroptions, departmentlistbanner, updatestatus } from "@/api/user";

    export default {
        name: 'product_productClassify',
        data () {
            return {
                grid: {
                    xl: 7,
                    lg: 7,
                    md: 12,
                    sm: 24,
                    xs: 24
                },
                loading: false,
                artFrom: {
                    page: 1,
                    dept_name: '',
                    limit: 10,
                },
                total: 0,
                tableData: [],
                editModal: false,          // 弹窗开关
                btnLoading: false,
                editForm: {
                    id: null,
                    dept_name: '',
                    desc: '',
                    image: '',
                    banners: []        // 轮播图数组
                },

                uploadUrl: 'https://67686161.com/adminapi/file/attachment/upload', // 上传地址
                headers: {             // 携带 token
                    'Content-Type': 'multipart/form-data',
                    'Authori-zation': 'Bearer ' + util.cookies.get('token')
                },
                detailDefaultList: [],       // Upload 的 default-file-list
                detailModal: false,
                detailForm: {
                    dept_name: '',
                    desc: '',
                    image: '',
                    banners: []
                },
                addModal: false,        // 新增弹窗
                addForm: {              // 新增表单
                    dept_name: '',
                    desc: '',
                    image: '',
                    banners: []
                },
                bannerModal: false,           // 轮播图弹窗开关
                bannerImages: []              // 弹窗里回显的轮播图列表
            }
        },
        computed: {
            ...mapState('admin/layout', [
                'isMobile'
            ]),
            ...mapState('admin/userLevel', [
                'categoryId'
            ]),
            labelWidth () {
                return this.isMobile ? undefined : 96;
            },
            labelPosition () {
                return this.isMobile ? 'top' : 'right';
            },



        },
        mounted () {
            this.getList();
        },
        methods: {
            onchangeIsShow (row) {
                let data = {
                    id: row.id,
                    status: row.status
                }
                updatestatus(data).then(res => {
                    this.$Message.success(res.msg);
                    this.getList()
                }).catch(res => {
                    this.$Message.error(res.msg);
                })
            },
            openBannerModal () {
                // 1. 调接口拿当前科室轮播图
                banneroptions().then(res => {
                    console.log(res.data, '罗欣欣')
                    this.bannerImages = res.data || []   // 根据后端实际字段调整
                    this.bannerModal = true
                })

            },
            async handleBannerUpload (file) {
                const formData = new FormData();
                formData.append('file', file);
                try {
                    const res = await axios.post('https://67686161.com/adminapi/file/attachment/upload', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                            'Authori-zation': 'Bearer ' + util.cookies.get('token')
                        }
                    });
                    // 追加到轮播图数组
                    console.log(res.data, '大嘎嘎嘎嘎嘎嘎')
                    this.bannerImages.push({ img: res.data.data.url });
                    console.log(this.bannerImages, '大大大大')
                } catch (e) {
                    this.$Message.error('轮播图上传失败');
                }
                return false;
            },
            removeBannerImage (idx) {
                this.bannerImages.splice(idx, 1)
            },
            submitBanner () {
                // 3. 最终提交整个列表
                console.log(this.bannerImages, '洛溪')
                departmentlistbanner({ list_banners: this.bannerImages }).then(res => {
                    this.$Message.success('轮播添加成功')
                    this.bannerModal = false
                })
            },

            del (id) {
                this.$Modal.confirm({
                    title: '确认删除',
                    content: '确定要删除该科室吗？此操作不可恢复！',
                    okText: '确定',
                    cancelText: '取消',
                    onOk: () => {
                        deldepartment({ id }).then(res => {
                            if (res.status === 200) {
                                this.$Message.success('删除成功');
                                this.getList();           // 刷新列表
                            } else {
                                this.$Message.error(res.msg || '删除失败');
                            }
                        }).catch(err => {
                            this.$Message.error(err.msg || '删除失败');
                        });
                    },
                    onCancel: () => {
                        // 用户点击取消不做任何操作
                    }
                });
            },
            openAddModal () {
                this.addForm = {
                    dept_name: '',
                    desc: '',
                    image: '',
                    banners: []
                };
                this.addModal = true;
            },
            submitAdd () {
                this.btnLoading = true;
                departmentAdd(this.addForm).then(() => {
                    this.$Message.success('新增成功');
                    this.addModal = false;
                    this.getList();          // 刷新列表
                }).finally(() => {
                    this.btnLoading = false;
                });
            },
            async beforeAddIconUpload (file) {
                return this.handleUpload(file, 'addForm', 'image');
            },
            async beforeAddDetailUpload (file) {
                return this.handleUpload(file, 'addForm', 'banners');
            },
            // 通用上传逻辑
            async handleUpload (file, formKey, field) {
                const formData = new FormData();
                formData.append('file', file);
                try {
                    const res = await axios.post('https://67686161.com/adminapi/file/attachment/upload', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                            'Authori-zation': 'Bearer ' + util.cookies.get('token')
                        }
                    });
                    const url = res.data.data.url;
                    if (field === 'banners') {
                        this[formKey].banners.push(url);
                    } else {
                        this[formKey][field] = url;
                    }
                } catch (e) {
                    this.$Message.error('上传失败');
                }
                return false;
            },
            // 打开详情弹窗
            showDetailDialog (id) {
                const row = this.tableData.find(item => item.id === id);
                if (!row) return;
                this.detailForm = {
                    dept_name: row.dept_name,
                    desc: row.desc,
                    image: row.image || '',
                    banners: row.banners || []
                };
                this.detailModal = true;
            },

            /* 打开弹窗并回显 */
            showReportDetailDialog (id) {
                const row = this.tableData.find(item => item.id === id);
                if (!row) return;
                this.editForm = {
                    id: row.id,
                    dept_name: row.dept_name,
                    desc: row.desc,
                    image: row.image || '',
                    banners: row.banners || []
                };
                // 设置 Upload 默认文件列表（需要 name/url 结构）
                this.detailDefaultList = (row.banners || []).map((url, index) => ({
                    name: 'img_' + index,
                    url
                }));
                this.editModal = true;
                this.$nextTick(() => {
                    // 重置 Upload 内部列表，防止缓存
                    this.$refs.iconUpload && (this.$refs.iconUpload.fileList = []);
                });
            },

            async beforeIconUpload (file) {
                const formData = new FormData();
                formData.append('file', file);

                try {
                    const res = await axios.post('https://67686161.com/adminapi/file/attachment/upload', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                            'Authori-zation': 'Bearer ' + util.cookies.get('token')
                        }
                    });
                    // 根据你后端返回格式取出 url
                    this.editForm.image = res.data.data.url;
                } catch (e) {
                    this.$Message.error('图标上传失败');
                }
                return false; // 阻止 Upload 默认上传
            },

            /* 多图上传：轮播图 */
            async beforeDetailUpload (file) {
                const formData = new FormData();
                formData.append('file', file);

                try {
                    const res = await axios.post('https://67686161.com/adminapi/file/attachment/upload', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                            'Authori-zation': 'Bearer ' + util.cookies.get('token')
                        }
                    });
                    // 追加到轮播图数组
                    this.editForm.banners.push(res.data.data.url);
                } catch (e) {
                    this.$Message.error('轮播图上传失败');
                }
                return false;
            },

            /* 删除单张轮播图 */
            removeDetailImage (file, fileList) {
                // 已经用 v-for 手动删除，这里可以空着
            },
            handleFormatError () {
                this.$Message.error('图片格式仅支持 jpg、png');
            },
            handleMaxSize () {
                this.$Message.error('图片大小不能超过 2M');
            },

            /* 提交修改 */
            submitEdit () {
                this.btnLoading = true;
                departmentUpdate(this.editForm).then(() => {
                    this.$Message.success('修改成功');
                    this.editModal = false;
                    this.getList();          // 刷新列表
                }).finally(() => {
                    this.btnLoading = false;
                });
            },
            changePatientPages (page) {
                this.artFrom.page = page;
                this.getList();
            },
            // 列表
            getList () {
                this.loading = true;
                departmentList(this.artFrom).then(async res => {
                    console.log(res, '罗鑫')
                    this.tableData = res.data.list;
                    this.total = res.data.count;
                    this.loading = false;
                }).catch(res => {
                    this.loading = false;
                    this.$Message.error(res.msg);
                })
            },
            pageChange (index) {
                this.artFrom.page = index;
                this.getList();
            },
            reset () {
                this.artFrom.dept_name = ''
                this.getList(this.artFrom);
            },
            // 表格搜索
            userSearchs () {
                this.artFrom.page = 1;
                this.getList();
            },
        }
    }
</script>
<style scoped lang="stylus">
    .treeSel >>>.ivu-select-dropdown-list
        padding 0 10px !important
        box-sizing border-box
    .tabBox_img
        width 36px
        height 36px
        border-radius 4px
        cursor pointer
        img
            width 100%
            height 100%
    /deep/.ivu-input
        font-size 14px !important
    .project-time-row
        display flex
        margin-bottom 10px
    .add-project-button
        margin-left 90px
        margin-bottom 30px
        border solid 1px #2D8CF0
        color #2D8CF0
        line-hight 10px
        .suffix
            position absolute
            right 10px
            top 50%
            transform translateY(-50%)
            font-size 14px
            color #1A1A1A
            pointer-events none
</style>
<style lang="less" scoped>
    .container {
        padding: 20px;
        background-color: #f5f5f5;
        height: 100%;
    }

    .gauge-canvas {
        width: 230px;
        height: 135px;
    }

    .report-card {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: relative; /* 添加 position 属性 */
    }

    .report-cards {
        height: 480px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        position: relative; /* 添加 position 属性 */
        z-index: 1; /* 设置 z-index 为 1，使其位于下层 */
    }

    .report-title {
        color: white;
        padding: 15px;
        text-align: left;
        font-size: 30px;
        font-weight: Semibold;
    }

    .report-subtitle {
        margin-top: 30px;
        color: white;
        padding: 10px;
        font-size: 14px;
        width: 150px;
        height: 40px;
        margin-left: 20px;
        text-align: left;
        border-radius: 10px;
        background-color: #1272cb; /* 基础背景色 */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5); /* 添加阴影效果 */
    }

    .score-container {
        position: absolute;
        top: 60px;
        right: -40px;
    }

    .score-gauge {
        width: 120px;
        height: 120px;
    }

    .score-value {
        font-size: 24px;
        font-weight: bold;
        margin-left: 10px;
    }

    .report-content {
        padding: 20px;
        background-color: #eff8ff;
        border-radius: 10px;
        position: absolute;
        top: 20px;
        width: calc(128% - 40px);
        z-index: 2; /* 设置 z-index 为 2，使其位于上层 */
        margin-top: 220px;
        margin-left: 20px;
    }

    .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .section-titleBBB {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding-left: 20px;
    }

    .section-text {
        font-size: 16px;
        font-weight: bold;
        color: #4a90e2;
    }

    .report-details {
        line-height: 1.6;
        color: #666;
    }

    .body-analysis {
        padding: 20px;
        // border-top: 1px solid #eee;
    }

    .body-analysisS {
        padding: 10px 0px 0px 40px;
        background-size: cover;
        background-position: center;
        position: relative; /* 添加 position 属性 */
        background-color: #f0f8ff;
        // border-radius: 10px;
        height: 40px; /* 设置背景图的高度 */
    }

    .analysis-title {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .analysis-titles {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        position: relative;
        z-index: 2; /* 确保文字显示在背景图的上层 */
    }

    .analysis-text {
        font-size: 16px;
        font-weight: bold;
        color: #4a90e2;
    }

    .analysis-texts {
        font-size: 16px;
        font-weight: bold;
        color: white;
    }

    .body-outline {
        position: relative;
        height: 400px;
        background-color: #f0f8ff;
        border-radius: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .body-icon {
        width: 200px;
        height: 400px;
        border-radius: 100px;
        position: relative;
    }

    .body-labels {
        position: absolute;
        width: 100%;
        height: 100%;
    }

    .label {
        position: absolute;
        background-color: rgba(255, 255, 255, 0.8);
        padding: 5px 10px;
        border-radius: 5px;
        font-size: 12px;
        color: #333;
    }

    /* 疾病部分样式 */
    .disease-section {
        padding: 10px;
        background-color: white;
        border-radius: 10px;
        margin-left: 20px;
        margin-right: 20px;
        // box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    /* 疾病部分样式 */
    .disease-sectionAAA {
        padding: 10px;
        background-color: white;
        border-radius: 10px;
        margin-right: 20px;
    }

    .disease-details {
        line-height: 1.6;
        color: #444;
        padding: 10px;
        background-color: white;
        border-radius: 8px;
        // box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .disease-text {
        margin-bottom: 8px;
        font-size: 14px;
    }

    .Adisease-section {
        background-color: white;
        border-radius: 10px;
        margin-left: 20px;
        margin-right: 20px;
    }

    .image-container {
        width: 300px;
        height: 300px;
        border-radius: 10px;
        overflow: hidden;
        margin: 20px 0;
        position: relative;
    }

    .tongue-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
    }
    .background {
        width: 62%;
        height: 70%;
        background-position: center; /* 背景图片居中 */
        background-repeat: no-repeat; /* 不重复背景图片 */
        background-size: cover; /* 背景图片覆盖整个容器 */
        position: absolute;
        margin-top: -88%;
        margin-left: 19%;
        border-radius: 15px;
        z-index: 2;
    }
</style>

